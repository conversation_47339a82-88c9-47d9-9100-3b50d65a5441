#!/usr/bin/env python3
"""
Build script for the Rust parser library.
This script compiles the Rust code and makes it available to Python.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return the result."""
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, cwd=cwd, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_rust_installed():
    """Check if Rust is installed."""
    try:
        result = subprocess.run(['rustc', '--version'], capture_output=True, text=True)
        print(f"Rust version: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        print("❌ Rust is not installed. Please install Rust from https://rustup.rs/")
        return False

def check_cargo_installed():
    """Check if Cargo is installed."""
    try:
        result = subprocess.run(['cargo', '--version'], capture_output=True, text=True)
        print(f"Cargo version: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        print("❌ Cargo is not installed. Please install Rust from https://rustup.rs/")
        return False

def install_maturin():
    """Install maturin for building Python extensions."""
    print("Installing maturin...")
    return run_command([sys.executable, '-m', 'pip', 'install', 'maturin'])

def build_rust_library():
    """Build the Rust library."""
    rust_dir = Path(__file__).parent / 'rust'
    
    if not rust_dir.exists():
        print(f"❌ Rust directory not found: {rust_dir}")
        return False
    
    print(f"Building Rust library in {rust_dir}")
    
    # Build with maturin
    if not run_command(['maturin', 'build', '--release'], cwd=rust_dir):
        print("❌ Failed to build Rust library with maturin")
        return False
    
    # Find the built wheel
    target_dir = rust_dir / 'target' / 'wheels'
    if target_dir.exists():
        wheels = list(target_dir.glob('*.whl'))
        if wheels:
            wheel_path = wheels[0]  # Take the first wheel found
            print(f"✅ Built wheel: {wheel_path}")
            
            # Install the wheel
            if run_command([sys.executable, '-m', 'pip', 'install', str(wheel_path), '--force-reinstall']):
                print("✅ Successfully installed Rust library")
                return True
            else:
                print("❌ Failed to install wheel")
                return False
        else:
            print("❌ No wheel files found in target/wheels")
            return False
    else:
        print("❌ target/wheels directory not found")
        return False

def test_import():
    """Test if the library can be imported."""
    try:
        import eterna_parser
        print("✅ Successfully imported eterna_parser")
        
        # Test a simple function
        result = eterna_parser.py_get_today_ymd()
        print(f"✅ Test function call successful: {result}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import eterna_parser: {e}")
        return False

def main():
    """Main build process."""
    print("🔧 Building Rust Parser Library")
    print("=" * 50)
    
    # Check prerequisites
    if not check_rust_installed():
        return False
    
    if not check_cargo_installed():
        return False
    
    # Install maturin if needed
    try:
        subprocess.run(['maturin', '--version'], capture_output=True, check=True)
        print("✅ Maturin is already installed")
    except (FileNotFoundError, subprocess.CalledProcessError):
        if not install_maturin():
            return False
    
    # Build the library
    if not build_rust_library():
        return False
    
    # Test the import
    if not test_import():
        return False
    
    print("\n🎉 Build completed successfully!")
    print("\nYou can now use the Rust-powered parser with:")
    print("  python manage.py parse-daemon-rust --help")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
