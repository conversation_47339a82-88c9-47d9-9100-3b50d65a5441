'''
NOTE this parser uses Rust implementation for heavy-lifting operations
'''

from django.conf import settings
from django.core.management.base import BaseCommand

from functools import partial
from multiprocessing import Pool, Process
from operator import itemgetter
from os import path, makedirs, remove
from shutil import rmtree
from signal import SIGINT, signal
from time import perf_counter
from typing import List, Optional, Tuple

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    abort,
    add_yearmonthday_force,
    colorize,
    convert_second,
    get_command,
    get_list_of_files,
    keyboard_interrupt_handler,
    save_log,
    to_tilda,
)

from base.utils_classes import (
    DaemonParser,
    DaemonConfig,
    MYSQLConfig,
)

from base.utils_constants import (
    ACTION_ON_ERROR,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils import (
    end_of_command_msg,
    filter_list,
    get_today_ymd,
    separator,
    source_log_info_line,
)

from base.models import Sensor

# Import the Rust library
try:
    import eterna_parser
    RUST_AVAILABLE = True
    print("✅ Rust parser library loaded successfully")
except ImportError as e:
    RUST_AVAILABLE = False
    print(f"❌ Rust parser library not available: {e}")
    print("Falling back to Python implementation")


signal(SIGINT, keyboard_interrupt_handler)


sensor_list_of_names               = Sensor.get_list_of_names()
sensor_list_of_names_and_addresses = Sensor.get_list_of_names_and_addresses()
sensor_dict_of_addresses_and_names = Sensor.get_dict_of_addresses_and_names()


class Command(BaseCommand):
    help = 'Parse daemon logs using Rust implementation'

    def add_arguments(self, parser):
        add_yearmonthday_force(parser)

    def handle(self, *args, **options):
        command = get_command(__file__)
        log_file = f'{settings.PROJECT_LOGS_DIR}/{command}.log'

        print(colorize(self, 'command', f'{command} started'))
        save_log(self, command, settings.HOST_NAME, log_file, f'{command} started')

        start_time = perf_counter()

        # Get command line arguments
        year_months = options.get('year_months', [])
        year_month_days = options.get('year_month_days', [])
        start_year_month = options.get('start_year_month')
        end_year_month = options.get('end_year_month')
        start_year_month_day = options.get('start_year_month_day')
        end_year_month_day = options.get('end_year_month_day')
        force = options.get('force', False)

        if not RUST_AVAILABLE:
            abort(self, 'Rust parser library is not available. Please build the Rust library first.')
            return

        try:
            # Get source logs using Rust implementation
            source_logs = eterna_parser.py_get_source_logs(
                settings.LOGS_DIR,
                year_months,
                year_month_days,
                start_year_month,
                end_year_month,
                start_year_month_day,
                end_year_month_day,
            )

            if not source_logs:
                save_log(self, command, settings.HOST_NAME, log_file, 'no source logs found')
                print(colorize(self, 'warning', 'no source logs found'))
                return

            print(f'Found {len(source_logs)} source log(s)')
            save_log(self, command, settings.HOST_NAME, log_file, f'found {len(source_logs)} source log(s)')

            for source_log_index, source_log in enumerate(source_logs, start=1):
                log_date = eterna_parser.py_get_date_of_source_log(source_log)
                
                # Check if log date is valid
                today_ymd = eterna_parser.py_get_today_ymd()
                if eterna_parser.py_is_invalid_log_date(log_date, today_ymd):
                    save_log(self, command, settings.HOST_NAME, log_file, f'skipping invalid log date: {log_date}')
                    continue

                print(separator())
                print(source_log_info_line(source_log, source_log_index, len(source_logs)))

                # Create Rust parser instance
                logs_parsed_dir = eterna_parser.py_get_daemon_logs_parsed_dir()
                rust_parser = eterna_parser.PyDaemonLogParser(
                    sensor_list_of_names,
                    log_date,
                    force,
                    logs_parsed_dir
                )

                # Check if all sensors are already accomplished
                accomplished_sensors = rust_parser.get_accomplished_sensors()
                if len(accomplished_sensors) == len(sensor_list_of_names) and not force:
                    print(colorize(self, 'already_parsed', f'{command}: {log_date} all sensors already parsed, skipping'))
                    continue

                # Parse the file using Rust
                print('parsing...')
                parse_start = perf_counter()
                
                try:
                    rust_parser.parse_file(
                        source_log,
                        sensor_list_of_names_and_addresses,
                        sensor_dict_of_addresses_and_names,
                        MYSQLConfig.POOL_CHUNKSIZE.value
                    )
                    
                    parse_end = perf_counter()
                    parse_duration = int(parse_end - parse_start)
                    print(f'parsed in {parse_duration:,} seconds ({eterna_parser.py_convert_second(parse_duration, False)})')
                    
                except Exception as e:
                    save_log(self, command, settings.HOST_NAME, log_file, f'error parsing {source_log}: {e}')
                    print(colorize(self, 'error', f'error parsing {source_log}: {e}'))
                    continue

                # Process each sensor
                database_name = rust_parser.get_database_name()
                
                for sensor_name in sensor_list_of_names:
                    if sensor_name in accomplished_sensors and not force:
                        continue

                    row_count = rust_parser.get_sensor_row_count(sensor_name)
                    if row_count == 0:
                        continue

                    print(f'Processing {sensor_name}: {row_count:,} rows')
                    save_log(self, command, settings.HOST_NAME, log_file, f'processing {sensor_name}: {row_count:,} rows')

                    try:
                        # Write to database using Rust
                        write_start = perf_counter()
                        rust_parser.write_sensor_to_database(sensor_name, log_file)
                        write_end = perf_counter()
                        write_duration = int(write_end - write_start)
                        
                        print(f'wrote {sensor_name} in {write_duration:,} seconds ({eterna_parser.py_convert_second(write_duration, False)})')
                        save_log(self, command, settings.HOST_NAME, log_file, f'wrote {sensor_name} in {write_duration:,} seconds')

                        # Create accomplished marker
                        eterna_parser.py_create_accomplished_file(logs_parsed_dir, sensor_name, log_date)
                        
                    except Exception as e:
                        save_log(self, command, settings.HOST_NAME, log_file, f'error writing {sensor_name}: {e}')
                        print(colorize(self, 'error', f'error writing {sensor_name}: {e}'))
                        continue

                # Create level count table
                try:
                    rust_parser.create_level_count_table(log_file)
                    save_log(self, command, settings.HOST_NAME, log_file, 'created level count table')
                except Exception as e:
                    save_log(self, command, settings.HOST_NAME, log_file, f'error creating level count table: {e}')
                    print(colorize(self, 'error', f'error creating level count table: {e}'))

                # Log database size
                try:
                    db_size = get_size_of_database(database_name, convert=True)
                    save_log(self, command, settings.HOST_NAME, log_file, f'database {database_name} size: {db_size}')
                except Exception as e:
                    save_log(self, command, settings.HOST_NAME, log_file, f'error getting database size: {e}')

        except Exception as e:
            save_log(self, command, settings.HOST_NAME, log_file, f'fatal error: {e}')
            print(colorize(self, 'error', f'fatal error: {e}'))
            raise

        finally:
            end_time = perf_counter()
            total_duration = int(end_time - start_time)
            
            save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {total_duration:,} seconds ({convert_second(total_duration, verbose=False)})')
            print(colorize(self, 'accomplished_in', f'accomplished in {total_duration:,} seconds ({convert_second(total_duration, verbose=False)})'))
            print(end_of_command_msg(self, command))
