use std::fs::{File, OpenOptions};
use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Write, <PERSON>ufWriter};
use std::path::Path;
use anyhow::{Result, anyhow};

use crate::rahavard::{get_list_of_files, get_today_ymd};
use crate::utils::{get_date_of_source_log, is_invalid_log_date, filter_list};

/// File operations for log parsing
pub struct FileOps;

impl FileOps {
    /// Get list of source log files based on criteria
    pub fn get_source_logs(
        logs_dir: &str,
        year_months: &[String],
        year_month_days: &[String],
        start_year_month: Option<&str>,
        end_year_month: Option<&str>,
        start_year_month_day: Option<&str>,
        end_year_month_day: Option<&str>,
    ) -> Result<Vec<String>> {
        // Get all log files
        let all_files = get_list_of_files(logs_dir, "log");
        
        // Extract dates from filenames and filter
        let mut log_dates: Vec<String> = all_files
            .iter()
            .map(|file| get_date_of_source_log(file))
            .collect();

        // Remove duplicates and sort
        log_dates.sort();
        log_dates.dedup();

        // Filter based on criteria
        let filtered_dates = filter_list(
            &log_dates,
            year_months,
            year_month_days,
            start_year_month,
            end_year_month,
            start_year_month_day,
            end_year_month_day,
        );

        // Filter out invalid dates (future dates)
        let today = get_today_ymd();
        let valid_dates: Vec<String> = filtered_dates
            .into_iter()
            .filter(|date| !is_invalid_log_date(date, &today))
            .collect();

        // Convert back to full file paths
        let mut source_logs = Vec::new();
        for date in valid_dates {
            for file in &all_files {
                if get_date_of_source_log(file) == date {
                    source_logs.push(file.clone());
                    break; // Only take first matching file for each date
                }
            }
        }

        Ok(source_logs)
    }

    /// Read lines from a file
    pub fn read_lines_from_file(file_path: &str) -> Result<Vec<String>> {
        let file = File::open(file_path)
            .map_err(|e| anyhow!("Failed to open file {}: {}", file_path, e))?;
        
        let reader = BufReader::new(file);
        let mut lines = Vec::new();

        for line in reader.lines() {
            match line {
                Ok(content) => lines.push(content),
                Err(e) => {
                    eprintln!("Error reading line from {}: {}", file_path, e);
                    // Continue reading other lines instead of failing completely
                }
            }
        }

        Ok(lines)
    }

    /// Write lines to a file (overwrite)
    pub fn write_lines_to_file(file_path: &str, lines: &[String]) -> Result<()> {
        let file = File::create(file_path)
            .map_err(|e| anyhow!("Failed to create file {}: {}", file_path, e))?;
        
        let mut writer = BufWriter::new(file);

        for line in lines {
            writeln!(writer, "{}", line)
                .map_err(|e| anyhow!("Failed to write line to {}: {}", file_path, e))?;
        }

        writer.flush()
            .map_err(|e| anyhow!("Failed to flush file {}: {}", file_path, e))?;

        Ok(())
    }

    /// Append lines to a file
    pub fn append_lines_to_file(file_path: &str, lines: &[String]) -> Result<()> {
        let file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(file_path)
            .map_err(|e| anyhow!("Failed to open file for append {}: {}", file_path, e))?;
        
        let mut writer = BufWriter::new(file);

        for line in lines {
            writeln!(writer, "{}", line)
                .map_err(|e| anyhow!("Failed to write line to {}: {}", file_path, e))?;
        }

        writer.flush()
            .map_err(|e| anyhow!("Failed to flush file {}: {}", file_path, e))?;

        Ok(())
    }

    /// Check if file exists
    pub fn file_exists(file_path: &str) -> bool {
        Path::new(file_path).exists()
    }

    /// Create directory if it doesn't exist
    pub fn create_directory(dir_path: &str) -> Result<()> {
        std::fs::create_dir_all(dir_path)
            .map_err(|e| anyhow!("Failed to create directory {}: {}", dir_path, e))
    }

    /// Remove file
    pub fn remove_file(file_path: &str) -> Result<()> {
        if Path::new(file_path).exists() {
            std::fs::remove_file(file_path)
                .map_err(|e| anyhow!("Failed to remove file {}: {}", file_path, e))?;
        }
        Ok(())
    }

    /// Get file size in bytes
    pub fn get_file_size(file_path: &str) -> Result<u64> {
        let metadata = std::fs::metadata(file_path)
            .map_err(|e| anyhow!("Failed to get metadata for {}: {}", file_path, e))?;
        Ok(metadata.len())
    }

    /// Write formatted infile data
    pub fn write_infile_data(
        file_path: &str,
        rows: &[Vec<String>],
        start_row_id: usize,
        terminated_by: &str,
        enclosed_by: &str,
    ) -> Result<()> {
        let file = File::create(file_path)
            .map_err(|e| anyhow!("Failed to create infile {}: {}", file_path, e))?;
        
        let mut writer = BufWriter::new(file);
        let mut row_id = start_row_id;

        for row in rows {
            row_id += 1;
            
            // Add ID to the beginning of each row
            let mut full_row = vec![row_id.to_string()];
            full_row.extend(row.iter().cloned());

            // Format each cell with enclosing characters
            let formatted_cells: Vec<String> = full_row
                .iter()
                .map(|cell| format!("{}{}{}", enclosed_by, cell, enclosed_by))
                .collect();

            // Join with terminator
            let formatted_row = formatted_cells.join(terminated_by);
            
            writeln!(writer, "{}", formatted_row)
                .map_err(|e| anyhow!("Failed to write row to {}: {}", file_path, e))?;
        }

        writer.flush()
            .map_err(|e| anyhow!("Failed to flush infile {}: {}", file_path, e))?;

        Ok(())
    }

    /// Create accomplished marker file
    pub fn create_accomplished_file(
        logs_parsed_dir: &str,
        sensor_name: &str,
        log_date: &str,
    ) -> Result<()> {
        let dir_path = format!("{}/{}/{}", logs_parsed_dir, sensor_name, log_date);
        Self::create_directory(&dir_path)?;

        let accomplished_file = format!("{}/{}-accomplished.log", dir_path, log_date);
        Self::write_lines_to_file(&accomplished_file, &[format!("Completed at: {}", chrono::Local::now())])?;

        Ok(())
    }

    /// Check if sensor is already accomplished
    pub fn is_sensor_accomplished(
        logs_parsed_dir: &str,
        sensor_name: &str,
        log_date: &str,
    ) -> bool {
        let accomplished_file = format!(
            "{}/{}/{}/{}-accomplished.log",
            logs_parsed_dir, sensor_name, log_date, log_date
        );
        Self::file_exists(&accomplished_file)
    }

    /// Get accomplished sensors for a date
    pub fn get_accomplished_sensors(
        logs_parsed_dir: &str,
        sensor_names: &[String],
        log_date: &str,
    ) -> Vec<String> {
        sensor_names
            .iter()
            .filter(|sensor_name| {
                Self::is_sensor_accomplished(logs_parsed_dir, sensor_name, log_date)
            })
            .cloned()
            .collect()
    }

    /// Clean up temporary infiles
    pub fn cleanup_infiles(infile_paths: &[String]) -> Result<()> {
        for path in infile_paths {
            if let Err(e) = Self::remove_file(path) {
                eprintln!("Warning: Failed to remove infile {}: {}", path, e);
                // Continue with other files instead of failing
            }
        }
        Ok(())
    }

    /// Read file in chunks for memory efficiency
    pub fn read_file_in_chunks<F>(file_path: &str, chunk_size: usize, mut processor: F) -> Result<()>
    where
        F: FnMut(Vec<String>) -> Result<()>,
    {
        let file = File::open(file_path)
            .map_err(|e| anyhow!("Failed to open file {}: {}", file_path, e))?;
        
        let reader = BufReader::new(file);
        let mut chunk = Vec::with_capacity(chunk_size);

        for line in reader.lines() {
            match line {
                Ok(content) => {
                    chunk.push(content);
                    if chunk.len() >= chunk_size {
                        processor(chunk.clone())?;
                        chunk.clear();
                    }
                }
                Err(e) => {
                    eprintln!("Error reading line from {}: {}", file_path, e);
                }
            }
        }

        // Process remaining lines
        if !chunk.is_empty() {
            processor(chunk)?;
        }

        Ok(())
    }
}
