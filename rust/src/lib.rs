use pyo3::prelude::*;
// use pyo3::types::{<PERSON><PERSON><PERSON><PERSON>, PyList}; // Unused for now
use std::collections::HashMap;

mod snort_classifications;
mod windows_server_audit_events;
mod utils;
mod utils_classes;
mod utils_database;
mod utils_patterns;
mod utils_parsers;
mod rahavard;
mod parser_core;
mod file_io;

use parser_core::DaemonLogParser;
use file_io::FileOps;
use utils_classes::{DaemonConfig, MYSQLValue};
use rahavard::{convert_second, append_log_to_file};

/// Python wrapper for daemon log parsing
#[pyclass]
struct PyDaemonLogParser {
    parser: DaemonLogParser,
}

#[pymethods]
impl PyDaemonLogParser {
    #[new]
    fn new(
        sensor_list_of_names: Vec<String>,
        log_date: String,
        force: bool,
        logs_parsed_dir: String,
    ) -> PyResult<Self> {
        let parser = DaemonLogParser::new(
            &sensor_list_of_names,
            log_date,
            force,
            &logs_parsed_dir,
        ).map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(e.to_string()))?;

        Ok(PyDaemonLogParser { parser })
    }

    /// Parse lines from a file
    fn parse_file(
        &mut self,
        file_path: String,
        sensor_list_of_names_and_addresses: Vec<String>,
        sensor_dict_of_addresses_and_names: HashMap<String, String>,
        chunk_size: Option<usize>,
    ) -> PyResult<()> {
        let lines = FileOps::read_lines_from_file(&file_path)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(e.to_string()))?;

        let chunk_size = chunk_size.unwrap_or(100_000);

        self.parser.parse_lines_parallel(
            lines,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
            chunk_size,
        ).map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(e.to_string()))?;

        Ok(())
    }

    /// Write data to database for a specific sensor
    fn write_sensor_to_database(
        &self,
        sensor_name: String,
        log_file: String,
    ) -> PyResult<()> {
        self.parser.write_to_database(&sensor_name, &log_file)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(e.to_string()))?;
        Ok(())
    }

    /// Create level count table
    fn create_level_count_table(&self, log_file: String) -> PyResult<()> {
        self.parser.create_level_count_table(&log_file)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(e.to_string()))?;
        Ok(())
    }

    /// Get number of rows for a sensor
    fn get_sensor_row_count(&self, sensor_name: String) -> PyResult<usize> {
        if let Some(instance) = self.parser.sensor_names_and_instances.get(&sensor_name) {
            Ok(instance.rows.len())
        } else {
            Ok(0)
        }
    }

    /// Get list of accomplished sensors
    fn get_accomplished_sensors(&self) -> PyResult<Vec<String>> {
        Ok(self.parser.already_accomplished.clone())
    }

    /// Get database name
    fn get_database_name(&self) -> PyResult<String> {
        Ok(self.parser.database_name.clone())
    }
}

/// Utility functions exposed to Python
#[pyfunction]
fn py_get_source_logs(
    logs_dir: String,
    year_months: Option<Vec<String>>,
    year_month_days: Option<Vec<String>>,
    start_year_month: Option<String>,
    end_year_month: Option<String>,
    start_year_month_day: Option<String>,
    end_year_month_day: Option<String>,
) -> PyResult<Vec<String>> {
    let year_months = year_months.unwrap_or_default();
    let year_month_days = year_month_days.unwrap_or_default();

    let source_logs = FileOps::get_source_logs(
        &logs_dir,
        &year_months,
        &year_month_days,
        start_year_month.as_deref(),
        end_year_month.as_deref(),
        start_year_month_day.as_deref(),
        end_year_month_day.as_deref(),
    ).map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(e.to_string()))?;

    Ok(source_logs)
}

#[pyfunction]
fn py_convert_second(seconds: f64, verbose: Option<bool>) -> PyResult<String> {
    let verbose = verbose.unwrap_or(true);
    Ok(convert_second(seconds, verbose))
}

#[pyfunction]
fn py_create_name_of_database(slug: String, ymd: Option<String>, object_name: Option<String>) -> PyResult<String> {
    let ymd = ymd.unwrap_or_default();
    let object_name = object_name.unwrap_or_default();
    Ok(utils::create_name_of_database(&slug, &ymd, &object_name))
}

#[pyfunction]
fn py_create_path_of_infile(database_name: String, table_name: String, chunk_number: Option<usize>) -> PyResult<String> {
    Ok(utils::create_path_of_infile(&database_name, &table_name, chunk_number))
}

#[pyfunction]
fn py_get_no_of_infiles(length: usize) -> PyResult<usize> {
    Ok(utils::get_no_of_infiles(length))
}

#[pyfunction]
fn py_evenly_sized_batches(total_length: usize, len_of_each_batch: Option<usize>) -> PyResult<Vec<Vec<usize>>> {
    let len_of_each_batch = len_of_each_batch.unwrap_or(10);
    Ok(utils::evenly_sized_batches(total_length, len_of_each_batch))
}

#[pyfunction]
fn py_get_date_of_source_log(log_path: String) -> PyResult<String> {
    Ok(utils::get_date_of_source_log(&log_path))
}

#[pyfunction]
fn py_is_invalid_log_date(log_date: String, today_ymd: String) -> PyResult<bool> {
    Ok(utils::is_invalid_log_date(&log_date, &today_ymd))
}

#[pyfunction]
fn py_get_today_ymd() -> PyResult<String> {
    Ok(rahavard::get_today_ymd())
}

#[pyfunction]
fn py_to_tilda(text: String) -> PyResult<String> {
    Ok(rahavard::to_tilda(&text))
}

#[pyfunction]
fn py_convert_byte(size_in_bytes: u64) -> PyResult<String> {
    Ok(rahavard::convert_byte(size_in_bytes))
}

#[pyfunction]
fn py_get_command(full_path: String, drop_extension: Option<bool>) -> PyResult<String> {
    let drop_extension = drop_extension.unwrap_or(true);
    Ok(rahavard::get_command(&full_path, drop_extension))
}

#[pyfunction]
fn py_append_log_to_file(dest_file: String, msg: String) -> PyResult<()> {
    append_log_to_file(&dest_file, &msg)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(e.to_string()))?;
    Ok(())
}

#[pyfunction]
fn py_get_daemon_table_name() -> PyResult<String> {
    Ok(DaemonConfig::get_table_name())
}

#[pyfunction]
fn py_get_daemon_logs_parsed_dir() -> PyResult<String> {
    Ok(DaemonConfig::get_logs_parsed_dir())
}

#[pyfunction]
fn py_get_daemon_db_keys() -> PyResult<String> {
    match DaemonConfig::DB_KEYS.value() {
        MYSQLValue::Str(keys) => Ok(keys),
        _ => Err(PyErr::new::<pyo3::exceptions::PyRuntimeError, _>("Invalid DB_KEYS configuration")),
    }
}

#[pyfunction]
fn py_get_daemon_db_columns() -> PyResult<String> {
    match DaemonConfig::DB_COLUMNS.value() {
        MYSQLValue::Str(columns) => Ok(columns),
        _ => Err(PyErr::new::<pyo3::exceptions::PyRuntimeError, _>("Invalid DB_COLUMNS configuration")),
    }
}

/// File operations exposed to Python
#[pyfunction]
fn py_file_exists(file_path: String) -> PyResult<bool> {
    Ok(FileOps::file_exists(&file_path))
}

#[pyfunction]
fn py_create_directory(dir_path: String) -> PyResult<()> {
    FileOps::create_directory(&dir_path)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(e.to_string()))?;
    Ok(())
}

#[pyfunction]
fn py_remove_file(file_path: String) -> PyResult<()> {
    FileOps::remove_file(&file_path)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(e.to_string()))?;
    Ok(())
}

#[pyfunction]
fn py_get_file_size(file_path: String) -> PyResult<u64> {
    FileOps::get_file_size(&file_path)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(e.to_string()))
}

#[pyfunction]
fn py_create_accomplished_file(logs_parsed_dir: String, sensor_name: String, log_date: String) -> PyResult<()> {
    FileOps::create_accomplished_file(&logs_parsed_dir, &sensor_name, &log_date)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(e.to_string()))?;
    Ok(())
}

#[pyfunction]
fn py_is_sensor_accomplished(logs_parsed_dir: String, sensor_name: String, log_date: String) -> PyResult<bool> {
    Ok(FileOps::is_sensor_accomplished(&logs_parsed_dir, &sensor_name, &log_date))
}

/// Python module definition
#[pymodule]
fn eterna_parser(m: &Bound<'_, PyModule>) -> PyResult<()> {
    m.add_class::<PyDaemonLogParser>()?;
    
    // Utility functions
    m.add_function(wrap_pyfunction!(py_get_source_logs, m)?)?;
    m.add_function(wrap_pyfunction!(py_convert_second, m)?)?;
    m.add_function(wrap_pyfunction!(py_create_name_of_database, m)?)?;
    m.add_function(wrap_pyfunction!(py_create_path_of_infile, m)?)?;
    m.add_function(wrap_pyfunction!(py_get_no_of_infiles, m)?)?;
    m.add_function(wrap_pyfunction!(py_evenly_sized_batches, m)?)?;
    m.add_function(wrap_pyfunction!(py_get_date_of_source_log, m)?)?;
    m.add_function(wrap_pyfunction!(py_is_invalid_log_date, m)?)?;
    m.add_function(wrap_pyfunction!(py_get_today_ymd, m)?)?;
    m.add_function(wrap_pyfunction!(py_to_tilda, m)?)?;
    m.add_function(wrap_pyfunction!(py_convert_byte, m)?)?;
    m.add_function(wrap_pyfunction!(py_get_command, m)?)?;
    m.add_function(wrap_pyfunction!(py_append_log_to_file, m)?)?;
    
    // Daemon config functions
    m.add_function(wrap_pyfunction!(py_get_daemon_table_name, m)?)?;
    m.add_function(wrap_pyfunction!(py_get_daemon_logs_parsed_dir, m)?)?;
    m.add_function(wrap_pyfunction!(py_get_daemon_db_keys, m)?)?;
    m.add_function(wrap_pyfunction!(py_get_daemon_db_columns, m)?)?;
    
    // File operations
    m.add_function(wrap_pyfunction!(py_file_exists, m)?)?;
    m.add_function(wrap_pyfunction!(py_create_directory, m)?)?;
    m.add_function(wrap_pyfunction!(py_remove_file, m)?)?;
    m.add_function(wrap_pyfunction!(py_get_file_size, m)?)?;
    m.add_function(wrap_pyfunction!(py_create_accomplished_file, m)?)?;
    m.add_function(wrap_pyfunction!(py_is_sensor_accomplished, m)?)?;
    
    Ok(())
}
