use std::collections::HashMap;
use anyhow::{Result, anyhow};
use rayon::prelude::*;

use crate::utils_classes::{<PERSON><PERSON>onfig, Daemon<PERSON>arser, MYSQLValue};
use crate::utils_parsers::{parse_ln, ConfigType};
use crate::utils::{
    create_name_of_database, 
    create_path_of_infile, 
    get_no_of_infiles, 
    evenly_sized_batches
};
use crate::utils_database::DatabaseOps;
use crate::rahavard::{convert_second, append_log_to_file};

/// Represents a parsed line result
#[derive(Debug, Clone)]
pub struct ParsedLine {
    pub sensor_name: Option<String>,
    pub parsed_data: Option<Vec<String>>,
}

/// Main parser structure for daemon logs
pub struct DaemonLogParser {
    pub sensor_names_and_instances: HashMap<String, DaemonParser>,
    pub already_accomplished: Vec<String>,
    pub log_date: String,
    pub database_name: String,
}

impl DaemonLogParser {
    /// Create new daemon log parser
    pub fn new(
        sensor_list_of_names: &[String],
        log_date: String,
        force: bool,
        logs_parsed_dir: &str,
    ) -> Result<Self> {
        let database_name = create_name_of_database("daemon", &log_date, "");
        
        // Find already accomplished sensors
        let mut already_accomplished = Vec::new();
        if !force {
            for sensor_name in sensor_list_of_names {
                let accomplished_file = format!(
                    "{}/{}/{}/{}-accomplished.log",
                    logs_parsed_dir, sensor_name, log_date, log_date
                );
                if std::path::Path::new(&accomplished_file).exists() {
                    already_accomplished.push(sensor_name.clone());
                }
            }
        }

        // Create parser instances for each sensor
        let mut sensor_names_and_instances = HashMap::new();
        for sensor_name in sensor_list_of_names {
            let parser = DaemonParser::new(
                "daemon".to_string(),
                log_date.clone(),
                sensor_name.clone(),
            );
            sensor_names_and_instances.insert(sensor_name.clone(), parser);
        }

        Ok(DaemonLogParser {
            sensor_names_and_instances,
            already_accomplished,
            log_date,
            database_name,
        })
    }

    /// Parse a single line
    pub fn parse_line(
        &self,
        line: &str,
        sensor_list_of_names_and_addresses: &[String],
        sensor_dict_of_addresses_and_names: &HashMap<String, String>,
    ) -> ParsedLine {
        let (sensor_name, parsed_ln) = parse_ln(
            line.trim(),
            ConfigType::Daemon,
            sensor_list_of_names_and_addresses,
            sensor_dict_of_addresses_and_names,
        );

        if let Some(ref name) = sensor_name {
            if self.already_accomplished.contains(name) {
                return ParsedLine {
                    sensor_name: None,
                    parsed_data: None,
                };
            }
        }

        ParsedLine {
            sensor_name,
            parsed_data: parsed_ln,
        }
    }

    /// Parse lines in parallel using rayon
    pub fn parse_lines_parallel(
        &mut self,
        lines: Vec<String>,
        sensor_list_of_names_and_addresses: &[String],
        sensor_dict_of_addresses_and_names: &HashMap<String, String>,
        chunk_size: usize,
    ) -> Result<()> {
        let start_time = std::time::Instant::now();
        
        // Parse lines in parallel
        let parsed_results: Vec<ParsedLine> = lines
            .par_chunks(chunk_size)
            .flat_map(|chunk| {
                chunk.par_iter().map(|line| {
                    self.parse_line(line, sensor_list_of_names_and_addresses, sensor_dict_of_addresses_and_names)
                }).collect::<Vec<_>>()
            })
            .collect();

        // Collect results into sensor instances
        for result in parsed_results {
            if let (Some(sensor_name), Some(parsed_data)) = (result.sensor_name, result.parsed_data) {
                if let Some(instance) = self.sensor_names_and_instances.get_mut(&sensor_name) {
                    instance.rows.push(parsed_data);
                }
            }
        }

        let duration = start_time.elapsed().as_secs();
        println!("parsed in {} seconds ({})", duration, convert_second(duration as f64, false));

        Ok(())
    }

    /// Write data to database for a specific sensor
    pub fn write_to_database(
        &self,
        sensor_name: &str,
        log_file: &str,
    ) -> Result<()> {
        let instance = self.sensor_names_and_instances.get(sensor_name)
            .ok_or_else(|| anyhow!("Sensor instance not found: {}", sensor_name))?;

        if instance.rows.is_empty() {
            return Ok(());
        }

        let table_name = DaemonConfig::get_table_name();
        let no_of_infiles = get_no_of_infiles(instance.rows.len());

        if no_of_infiles == 0 {
            return Ok(());
        }

        // Log progress
        append_log_to_file(log_file, &format!(
            "{} rows will be inserted into database", 
            instance.rows.len()
        ))?;

        // Create table
        let db_columns = match DaemonConfig::DB_COLUMNS.value() {
            MYSQLValue::Str(cols) => cols,
            _ => return Err(anyhow!("Invalid DB_COLUMNS configuration")),
        };

        DatabaseOps::create_table(&self.database_name, &table_name, &db_columns)?;
        append_log_to_file(log_file, &format!("creating table {}", table_name))?;

        // Create infiles in batches
        let batches = evenly_sized_batches(no_of_infiles, 10); // Process 10 infiles at a time
        let mut infile_paths = Vec::new();

        for batch in batches {
            // Create infiles for this batch
            for &infile_index in &batch {
                let infile_path = create_path_of_infile(
                    &self.database_name, 
                    &table_name, 
                    Some(infile_index)
                );
                
                self.write_infile(&infile_path, instance, infile_index, no_of_infiles)?;
                infile_paths.push(infile_path);
            }

            // Load data from infiles
            for infile_path in &infile_paths {
                if std::path::Path::new(infile_path).exists() {
                    append_log_to_file(log_file, &format!("inserting from {}", infile_path))?;
                    
                    let db_keys = match DaemonConfig::DB_KEYS.value() {
                        MYSQLValue::Str(keys) => keys,
                        _ => return Err(anyhow!("Invalid DB_KEYS configuration")),
                    };

                    DatabaseOps::load_data_infile(
                        &self.database_name,
                        &table_name,
                        infile_path,
                        &db_keys,
                    )?;

                    // Clean up infile
                    let _ = std::fs::remove_file(infile_path);
                }
            }
            infile_paths.clear();
        }

        Ok(())
    }

    /// Write infile for a specific chunk
    fn write_infile(
        &self,
        infile_path: &str,
        instance: &DaemonParser,
        infile_index: usize,
        no_of_infiles: usize,
    ) -> Result<()> {
        use std::io::Write;

        let chunk_size = match crate::utils_classes::MYSQLConfig::INFILE_CHUNKSIZE.value() {
            MYSQLValue::Int(size) => size,
            _ => 5_000_000,
        };

        let start_of_chunk = chunk_size * (infile_index - 1);
        let end_of_chunk = std::cmp::min(start_of_chunk + chunk_size, instance.rows.len());

        let terminated_by = match crate::utils_classes::MYSQLConfig::TERMINATED_BY.value() {
            MYSQLValue::Str(term) => term,
            _ => "-*@*-".to_string(),
        };

        let enclosed_by = match crate::utils_classes::MYSQLConfig::ENCLOSED_BY.value() {
            MYSQLValue::Str(enc) => enc,
            _ => "".to_string(),
        };

        let mut file = std::fs::File::create(infile_path)?;
        let mut row_id = start_of_chunk + 1;

        for row in &instance.rows[start_of_chunk..end_of_chunk] {
            let mut full_row = vec![row_id.to_string()];
            full_row.extend(row.iter().cloned());

            let formatted_row = full_row
                .iter()
                .map(|cell| format!("{}{}{}", enclosed_by, cell, enclosed_by))
                .collect::<Vec<_>>()
                .join(&terminated_by);

            writeln!(file, "{}", formatted_row)?;
            row_id += 1;
        }

        Ok(())
    }

    /// Create level count table
    pub fn create_level_count_table(&self, log_file: &str) -> Result<()> {
        let mut count_rows = Vec::new();
        let mut total_errors = 0;
        let mut total_warnings = 0;

        // Count errors and warnings from all sensor instances
        for instance in self.sensor_names_and_instances.values() {
            total_errors += instance.daemon_errors_count;
            total_warnings += instance.daemon_warnings_count;
        }

        if total_errors > 0 {
            count_rows.push(vec!["Error".to_string(), total_errors.to_string()]);
        }
        if total_warnings > 0 {
            count_rows.push(vec!["Warning".to_string(), total_warnings.to_string()]);
        }

        if !count_rows.is_empty() {
            let table_name = "levelcounttable";
            let table_columns = format!(
                "ID {}, Level {}, Count {}",
                match crate::utils_classes::MYSQLConfig::ID_DATA_TYPE.value() {
                    MYSQLValue::Str(t) => t,
                    _ => "INT PRIMARY KEY AUTO_INCREMENT".to_string(),
                },
                match crate::utils_classes::MYSQLConfig::DEFAULT_DATA_TYPE.value() {
                    MYSQLValue::Str(t) => t,
                    _ => "MEDIUMTEXT".to_string(),
                },
                match crate::utils_classes::MYSQLConfig::COUNT_DATA_TYPE.value() {
                    MYSQLValue::Str(t) => t,
                    _ => "INT".to_string(),
                }
            );

            DatabaseOps::create_table(&self.database_name, table_name, &table_columns)?;
            append_log_to_file(log_file, &format!("creating table {}", table_name))?;

            DatabaseOps::insert_many(
                &self.database_name,
                table_name,
                "Level,Count",
                &count_rows,
            )?;

            append_log_to_file(log_file, &format!(
                "inserting {} rows into {}", 
                count_rows.len(), 
                table_name
            ))?;
        }

        Ok(())
    }
}
