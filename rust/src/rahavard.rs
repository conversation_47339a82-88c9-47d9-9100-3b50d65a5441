use std::fs;
use std::path::Path;
use regex::Regex;
use chrono::Local;
use anyhow::Result;

/// Convert seconds to human-readable format
pub fn convert_second(seconds: f64, verbose: bool) -> String {
    if seconds == 0.0 {
        return if verbose { "0".to_string() } else { "0:00".to_string() };
    }

    if seconds < 1.0 {
        return if verbose { "~0".to_string() } else { "~0:00".to_string() };
    }

    let seconds = seconds as i64;
    let ss = seconds % 60;
    let mi = (seconds / 60) % 60;
    let hh = (seconds / 3600) % 24;
    let dd = (seconds / 3600 / 24) % 30;
    let mo = (seconds / 3600 / 24 / 30) % 12;
    let yy = seconds / 3600 / 24 / 30 / 12;

    if verbose {
        let mut parts = Vec::new();
        if yy > 0 { parts.push(format!("{} year{}", yy, if yy == 1 { "" } else { "s" })); }
        if mo > 0 { parts.push(format!("{} month{}", mo, if mo == 1 { "" } else { "s" })); }
        if dd > 0 { parts.push(format!("{} day{}", dd, if dd == 1 { "" } else { "s" })); }
        if hh > 0 { parts.push(format!("{} hr{}", hh, if hh == 1 { "" } else { "s" })); }
        if mi > 0 { parts.push(format!("{} min{}", mi, if mi == 1 { "" } else { "s" })); }
        if ss > 0 { parts.push(format!("{} sec{}", ss, if ss == 1 { "" } else { "s" })); }

        match parts.len() {
            0 => "0".to_string(),
            1 => parts[0].clone(),
            2 => format!("{} and {}", parts[0], parts[1]),
            _ => {
                let last = parts.pop().unwrap();
                format!("{} and {}", parts.join(", "), last)
            }
        }
    } else {
        if yy == 0 && mo == 0 && dd == 0 {
            format!("{:02}:{:02}:{:02}", hh, mi, ss)
        } else if yy == 0 && mo == 0 {
            format!("{:02}:{:02}:{:02}:{:02}", dd, hh, mi, ss)
        } else if yy == 0 {
            format!("{:02}:{:02}:{:02}:{:02}:{:02}", mo, dd, hh, mi, ss)
        } else {
            format!("{:02}:{:02}:{:02}:{:02}:{:02}:{:02}", yy, mo, dd, hh, mi, ss)
        }
    }
}

/// Get list of files with specific extension in a directory
pub fn get_list_of_files(directory: &str, extension: &str) -> Vec<String> {
    if !Path::new(directory).exists() {
        return Vec::new();
    }

    let mut files = Vec::new();
    if let Ok(entries) = fs::read_dir(directory) {
        for entry in entries.flatten() {
            if let Ok(file_type) = entry.file_type() {
                if file_type.is_file() {
                    if let Some(file_name) = entry.file_name().to_str() {
                        if file_name.ends_with(&format!(".{}", extension)) {
                            if let Some(path_str) = entry.path().to_str() {
                                files.push(path_str.to_string());
                            }
                        }
                    }
                }
            }
        }
    }

    // Sort naturally (similar to natsorted in Python)
    files.sort();
    files
}

/// Replace home directory path with tilde
pub fn to_tilda(text: &str) -> String {
    if let Ok(home) = std::env::var("HOME") {
        text.replace(&home, "~")
    } else {
        text.to_string()
    }
}

/// Check if string contains date in YYYY-MM-DD format
pub fn contains_ymd(string: &str) -> bool {
    let ymd_regex = Regex::new(r"\d{4}-\d{2}-\d{2}").unwrap();
    ymd_regex.is_match(string)
}

/// Check if string is exactly in YYYY-MM-DD format
pub fn is_ymd(string: &str) -> bool {
    let ymd_regex = Regex::new(r"^\d{4}-\d{2}-\d{2}$").unwrap();
    ymd_regex.is_match(string)
}

/// Check if string starts with YYYY-MM-DD HH:MM:SS format
pub fn starts_with_ymdhms(string: &str) -> bool {
    let ymdhms_regex = Regex::new(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} ").unwrap();
    ymdhms_regex.is_match(string)
}

/// Get current date in YYYY-MM-DD format
pub fn get_today_ymd() -> String {
    Local::now().format("%Y-%m-%d").to_string()
}

/// Convert bytes to human-readable format
pub fn convert_byte(size_in_bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    
    if size_in_bytes == 0 {
        return "0B".to_string();
    }

    let mut size = size_in_bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if size.fract() == 0.0 {
        format!("{:.0}{}", size, UNITS[unit_index])
    } else {
        format!("{:.1}{}", size, UNITS[unit_index])
    }
}

/// Get command name from full path
pub fn get_command(full_path: &str, drop_extension: bool) -> String {
    let path = Path::new(full_path);
    let file_name = path.file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("");

    if drop_extension {
        path.file_stem()
            .and_then(|stem| stem.to_str())
            .unwrap_or(file_name)
            .to_string()
    } else {
        file_name.to_string()
    }
}

/// Save log message to file with timestamp
pub fn save_log_to_file(dest_file: &str, msg: &str) -> Result<()> {
    let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S");
    let log_line = format!("{} {}\n", timestamp, to_tilda(msg));
    
    fs::write(dest_file, log_line)?;
    Ok(())
}

/// Append log message to file with timestamp
pub fn append_log_to_file(dest_file: &str, msg: &str) -> Result<()> {
    let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S");
    let log_line = format!("{} {}\n", timestamp, to_tilda(msg));
    
    use std::fs::OpenOptions;
    use std::io::Write;
    
    let mut file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(dest_file)?;
    
    file.write_all(log_line.as_bytes())?;
    Ok(())
}

/// Calculate offset for pagination
pub fn calculate_offset(page_number: i32, limit_to_show: i32) -> i32 {
    (page_number - 1) * limit_to_show
}

/// Check if a number is integer or float
pub fn is_int_or_float(string: &str) -> bool {
    let pattern = Regex::new(r"^[0-9\.]+$").unwrap();
    pattern.is_match(string)
}

/// Sort dictionary by key or value
pub fn sort_dict_by_key<T: Clone + Ord>(dict: &std::collections::HashMap<String, T>) -> Vec<(String, T)> {
    let mut items: Vec<_> = dict.iter().map(|(k, v)| (k.clone(), v.clone())).collect();
    items.sort_by(|a, b| a.0.cmp(&b.0));
    items
}

pub fn sort_dict_by_value<T: Clone + Ord>(dict: &std::collections::HashMap<String, T>) -> Vec<(String, T)> {
    let mut items: Vec<_> = dict.iter().map(|(k, v)| (k.clone(), v.clone())).collect();
    items.sort_by(|a, b| a.1.cmp(&b.1));
    items
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_convert_second() {
        assert_eq!(convert_second(0.0, true), "0");
        assert_eq!(convert_second(0.0, false), "0:00");
        assert_eq!(convert_second(0.5, true), "~0");
        assert_eq!(convert_second(0.5, false), "~0:00");
        assert_eq!(convert_second(3661.0, true), "1 hr, 1 min and 1 sec");
        assert_eq!(convert_second(3661.0, false), "01:01:01");
    }

    #[test]
    fn test_is_ymd() {
        assert!(is_ymd("2023-10-05"));
        assert!(!is_ymd("05-10-2023"));
        assert!(!is_ymd("2023/10/05"));
        assert!(!is_ymd("20231005"));
    }

    #[test]
    fn test_contains_ymd() {
        assert!(contains_ymd("Today's date is 2023-10-05."));
        assert!(!contains_ymd("No date here!"));
        assert!(contains_ymd("The event is on 2023-12-25."));
        assert!(!contains_ymd("Date: 2023/10/05"));
    }

    #[test]
    fn test_convert_byte() {
        assert_eq!(convert_byte(0), "0B");
        assert_eq!(convert_byte(1024), "1KB");
        assert_eq!(convert_byte(1048576), "1MB");
        assert_eq!(convert_byte(1536), "1.5KB");
    }
}
