use chrono::{
    NaiveDate,
    NaiveTime,
    Datelike,
};
use regex::Regex;


pub fn normalize_date(date: &str) -> String {
    // __HAS_TEST__

    // first try parsing 2-digit year format with manual fix
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%y") {
        let year = parsed.year();
        let fixed_year = if year < 100 {
            if year >= 70 { 1900 + year } else { 2000 + year }
        } else {
            year
        };

        if let Some(fixed_date) = NaiveDate::from_ymd_opt(fixed_year, parsed.month(), parsed.day()) {
            return fixed_date.format("%Y-%m-%d").to_string();
        }
    }

    // then try parsing 4-digit year normally
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%Y") {
        return parsed.format("%Y-%m-%d").to_string();
    }

    // if all formats fail,
    // return the original date string
    date.to_string()
}


pub fn normalize_dns_question_name(url: &str) -> String {
    // __HAS_TEST__

    let re = Regex::new(r"\([0-9]+\)").unwrap();
    let normalized = re.replace_all(url, ".");
    normalized.trim_matches('.').to_string()
}


pub fn normalize_time(time: &str) -> String {
    // __HAS_TEST__

    // try parsing with 12-hour format with AM/PM (%I:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%I:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // try parsing with 24-hour format with AM/PM (%H:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%H:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // if all formats fail,
    // return the original time string
    time.to_string()
}

use std::path::Path;
use crate::utils_classes::{MYSQLConfig, MYSQLValue};

/// Create database name from slug, date, and object name
pub fn create_name_of_database(slug: &str, ymd: &str, object_name: &str) -> String {
    let non_dated_dbs = match MYSQLConfig::NON_DATED_DATABASES.value() {
        MYSQLValue::List(dbs) => dbs,
        _ => vec![],
    };

    if non_dated_dbs.contains(&slug.to_string()) {
        return slug.to_string();
    }

    let separator = match MYSQLConfig::DB_NAME_SEPARATOR.value() {
        MYSQLValue::Str(sep) => sep,
        _ => "__".to_string(),
    };

    let ymd_underscore = ymd.replace('-', "_");

    if object_name.is_empty() {
        format!("{}{}{}", slug, separator, ymd_underscore)
    } else {
        let object_name_underscore = object_name.replace('-', "_");
        format!("{}{}{}{}{}", slug, separator, object_name_underscore, separator, ymd_underscore)
    }
}

/// Create path for infile
pub fn create_path_of_infile(database_name: &str, table_name: &str, chunk_number: Option<usize>) -> String {
    let suffix = if let Some(chunk) = chunk_number {
        format!("__chunk_{}", chunk)
    } else {
        String::new()
    };

    format!("/tmp/infile__{}__{}{}.csv", database_name, table_name, suffix)
}

/// Get number of infiles needed based on length
pub fn get_no_of_infiles(length: usize) -> usize {
    let chunk_size = match MYSQLConfig::INFILE_CHUNKSIZE.value() {
        MYSQLValue::Int(size) => size,
        _ => 5_000_000,
    };

    if length == 0 {
        return 0;
    }

    (length + chunk_size - 1) / chunk_size // Ceiling division
}

/// Create evenly sized batches
pub fn evenly_sized_batches(total_length: usize, len_of_each_batch: usize) -> Vec<Vec<usize>> {
    let mut batches = Vec::new();
    let range: Vec<usize> = (1..=total_length).collect();

    for chunk in range.chunks(len_of_each_batch) {
        batches.push(chunk.to_vec());
    }

    batches
}

/// Extract date from log file path
pub fn get_date_of_source_log(log_path: &str) -> String {
    let path = Path::new(log_path);
    let file_name = path.file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("");

    // Remove '--Day.log' part and return only 'YYYY-MM-DD' part
    let re = Regex::new(r"--.*$").unwrap();
    re.replace(file_name, "").to_string()
}

/// Check if log date is invalid (future date)
pub fn is_invalid_log_date(log_date: &str, today_ymd: &str) -> bool {
    // Simple string comparison for YYYY-MM-DD format
    log_date > today_ymd
}

/// Filter list based on date criteria
pub fn filter_list(
    list_of_items: &[String],
    year_months: &[String],
    year_month_days: &[String],
    start_year_month: Option<&str>,
    end_year_month: Option<&str>,
    start_year_month_day: Option<&str>,
    end_year_month_day: Option<&str>,
) -> Vec<String> {
    let mut filtered = list_of_items.to_vec();

    // Filter by specific year-months
    if !year_months.is_empty() {
        filtered.retain(|item| {
            year_months.iter().any(|ym| {
                let ym_dash = ym.replace('_', "-");
                item.starts_with(&ym_dash)
            })
        });
    }

    // Filter by specific year-month-days
    if !year_month_days.is_empty() {
        filtered.retain(|item| {
            year_month_days.iter().any(|ymd| {
                let ymd_dash = ymd.replace('_', "-");
                item.contains(&ymd_dash)
            })
        });
    }

    // Filter by start year-month
    if let Some(start_ym) = start_year_month {
        let start_ym_dash = start_ym.replace('_', "-");
        filtered.retain(|item| item >= &start_ym_dash);
    }

    // Filter by end year-month
    if let Some(end_ym) = end_year_month {
        let end_ym_dash = end_ym.replace('_', "-");
        filtered.retain(|item| item <= &end_ym_dash);
    }

    // Filter by start year-month-day
    if let Some(start_ymd) = start_year_month_day {
        let start_ymd_dash = start_ymd.replace('_', "-");
        filtered.retain(|item| item >= &start_ymd_dash);
    }

    // Filter by end year-month-day
    if let Some(end_ymd) = end_year_month_day {
        let end_ymd_dash = end_ymd.replace('_', "-");
        filtered.retain(|item| item <= &end_ymd_dash);
    }

    filtered
}

/// Create separator line
pub fn separator() -> String {
    "-".repeat(80) // Default terminal width approximation
}

/// Create end of command message
pub fn end_of_command_msg(command: &str) -> String {
    let msg = format!(">>> END of {} <<<", command);
    let separator_line = separator();
    let padding = (separator_line.len().saturating_sub(msg.len())) / 2;
    let centered_msg = format!("{}{}{}", " ".repeat(padding), msg, " ".repeat(padding));
    format!("{}\n", centered_msg)
}

/// Create source log info line
pub fn source_log_info_line(
    source_log: &str,
    source_log_index: usize,
    source_logs_len: usize,
) -> String {
    let left_msg = format!("source log: {}", crate::rahavard::to_tilda(source_log));

    let right_msg = if source_logs_len == 1 {
        // Would need file size calculation here
        "".to_string()
    } else {
        let percentage = (source_log_index * 100) / source_logs_len;
        format!("{}/{} {}%", source_log_index, source_logs_len, percentage)
    };

    let terminal_width: usize = 80; // Default approximation
    let gap_len = terminal_width.saturating_sub(left_msg.len() + right_msg.len());

    format!("{}{}{}", left_msg, " ".repeat(gap_len), right_msg)
}
