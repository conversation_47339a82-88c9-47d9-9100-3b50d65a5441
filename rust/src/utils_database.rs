use mysql::*;
use mysql::prelude::*;
use anyhow::{Result, anyhow};
// use std::collections::HashMap; // Unused for now

use crate::utils_classes::{MYSQLConfig, MYSQLValue};

/// Database connection configuration
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub host: String,
    pub user: String,
    pub password: String,
    pub database: Option<String>,
}

impl DatabaseConfig {
    /// Create master credentials configuration
    pub fn master_creds() -> Result<Self> {
        let host = match MYSQLConfig::MYSQL_HOST.value() {
            MYSQLValue::Str(h) => h,
            _ => return Err(anyhow!("Invalid MYSQL_HOST configuration")),
        };
        let user = match MYSQLConfig::MYSQL_MASTER.value() {
            MYSQLValue::Str(u) => u,
            _ => return Err(anyhow!("Invalid MYSQL_MASTER configuration")),
        };
        let password = match MYSQLConfig::MYSQL_MASTER_PASSWD.value() {
            MYSQLValue::Str(p) => p,
            _ => return Err(anyhow!("Invalid MYSQL_MASTER_PASSWD configuration")),
        };

        Ok(DatabaseConfig {
            host,
            user,
            password,
            database: None,
        })
    }

    /// Create read-only user credentials configuration
    pub fn r_user_creds() -> Result<Self> {
        let host = match MYSQLConfig::MYSQL_HOST.value() {
            MYSQLValue::Str(h) => h,
            _ => return Err(anyhow!("Invalid MYSQL_HOST configuration")),
        };
        let user = match MYSQLConfig::MYSQL_R_USER.value() {
            MYSQLValue::Str(u) => u,
            _ => return Err(anyhow!("Invalid MYSQL_R_USER configuration")),
        };
        let password = match MYSQLConfig::MYSQL_R_USER_PASSWD.value() {
            MYSQLValue::Str(p) => p,
            _ => return Err(anyhow!("Invalid MYSQL_R_USER_PASSWD configuration")),
        };

        Ok(DatabaseConfig {
            host,
            user,
            password,
            database: None,
        })
    }

    /// Set database name
    pub fn with_database(mut self, database: String) -> Self {
        self.database = Some(database);
        self
    }

    /// Create MySQL connection URL
    pub fn to_url(&self) -> String {
        let base_url = format!("mysql://{}:{}@{}", self.user, self.password, self.host);
        if let Some(ref db) = self.database {
            format!("{}/{}", base_url, db)
        } else {
            base_url
        }
    }
}

/// Database operations
pub struct DatabaseOps;

impl DatabaseOps {
    /// Check if database exists
    pub fn database_exists(database_name: &str) -> Result<bool> {
        let config = DatabaseConfig::r_user_creds()?;
        let pool = Pool::new(config.to_url().as_str())?;
        let mut conn = pool.get_conn()?;

        let result: Option<String> = conn.exec_first(
            "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?",
            (database_name,)
        )?;

        Ok(result.is_some())
    }

    /// Check if table exists in database
    pub fn table_exists(database_name: &str, table_name: &str) -> Result<bool> {
        let tables = Self::get_tables(database_name)?;
        Ok(tables.contains(&table_name.to_string()))
    }

    /// Get list of databases
    pub fn get_databases(include_builtins: bool) -> Result<Vec<String>> {
        let config = DatabaseConfig::r_user_creds()?;
        let pool = Pool::new(config.to_url().as_str())?;
        let mut conn = pool.get_conn()?;

        let databases: Vec<String> = conn.query("SHOW DATABASES")?;

        if include_builtins {
            Ok(databases)
        } else {
            let builtin_dbs = match MYSQLConfig::BUILTIN_DATABASES.value() {
                MYSQLValue::List(dbs) => dbs,
                _ => return Err(anyhow!("Invalid BUILTIN_DATABASES configuration")),
            };

            Ok(databases.into_iter()
                .filter(|db| !builtin_dbs.contains(db))
                .collect())
        }
    }

    /// Get list of tables in a database
    pub fn get_tables(database_name: &str) -> Result<Vec<String>> {
        let config = DatabaseConfig::r_user_creds()?.with_database(database_name.to_string());
        let pool = Pool::new(config.to_url().as_str())?;
        let mut conn = pool.get_conn()?;

        let tables: Vec<String> = conn.exec(
            "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ?",
            (database_name,)
        )?;

        Ok(tables)
    }

    /// Get maximum ID from a table
    pub fn get_max_id(database_name: &str, table_name: &str) -> Result<i64> {
        let config = DatabaseConfig::r_user_creds()?.with_database(database_name.to_string());
        let pool = Pool::new(config.to_url().as_str())?;
        let mut conn = pool.get_conn()?;

        let max_id: Option<i64> = conn.exec_first(
            &format!("SELECT MAX(ID) FROM `{}`", table_name),
            ()
        )?;

        Ok(max_id.unwrap_or(0))
    }

    /// Create table with given SQL
    pub fn create_table(database_name: &str, table_name: &str, columns_sql: &str) -> Result<()> {
        let config = DatabaseConfig::master_creds()?.with_database(database_name.to_string());
        let pool = Pool::new(config.to_url().as_str())?;
        let mut conn = pool.get_conn()?;

        let create_sql = format!("CREATE TABLE {} ({})", table_name, columns_sql);
        conn.query_drop(create_sql)?;

        Ok(())
    }

    /// Execute LOAD DATA INFILE statement
    pub fn load_data_infile(
        database_name: &str,
        table_name: &str,
        infile_path: &str,
        db_keys: &str,
    ) -> Result<()> {
        let config = DatabaseConfig::master_creds()?.with_database(database_name.to_string());
        let pool = Pool::new(config.to_url().as_str())?;
        let mut conn = pool.get_conn()?;

        let terminated_by = match MYSQLConfig::TERMINATED_BY.value() {
            MYSQLValue::Str(t) => t,
            _ => return Err(anyhow!("Invalid TERMINATED_BY configuration")),
        };

        let enclosed_by = match MYSQLConfig::ENCLOSED_BY.value() {
            MYSQLValue::Str(e) => e,
            _ => return Err(anyhow!("Invalid ENCLOSED_BY configuration")),
        };

        // Start transaction
        conn.query_drop("SET UNIQUE_CHECKS=0")?;
        conn.query_drop("SET FOREIGN_KEY_CHECKS=0")?;
        conn.query_drop("START TRANSACTION")?;

        let infile_statement = MYSQLConfig::get_infile_statement();
        let load_sql = format!(
            r#"{} "{}"
            INTO TABLE {}
            FIELDS TERMINATED BY "{}"
            ENCLOSED BY '{}'
            LINES TERMINATED BY "\n"
            (ID,{})"#,
            infile_statement, infile_path, table_name, terminated_by, enclosed_by, db_keys
        );

        conn.query_drop(load_sql)?;
        conn.query_drop("COMMIT")?;

        Ok(())
    }

    /// Insert multiple rows using executemany
    pub fn insert_many(
        database_name: &str,
        table_name: &str,
        keys: &str,
        values: &[Vec<String>],
    ) -> Result<()> {
        let config = DatabaseConfig::master_creds()?.with_database(database_name.to_string());
        let pool = Pool::new(config.to_url().as_str())?;
        let mut conn = pool.get_conn()?;

        let placeholders = values.first()
            .map(|row| vec!["?"; row.len()].join(","))
            .unwrap_or_default();

        let insert_sql = format!(
            "INSERT INTO {} ({}) VALUES ({})",
            table_name, keys, placeholders
        );

        conn.query_drop("START TRANSACTION")?;

        for row in values {
            conn.exec_drop(&insert_sql, row)?;
        }

        conn.query_drop("COMMIT")?;

        Ok(())
    }
}
